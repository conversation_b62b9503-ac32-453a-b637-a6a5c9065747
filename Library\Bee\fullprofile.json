{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3352, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3352, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3352, "tid": 274, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3352, "tid": 274, "ts": 1756707417237998, "dur": 298, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3352, "tid": 274, "ts": 1756707417239710, "dur": 477, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3352, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415261540, "dur": 8852, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415270394, "dur": 1963912, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415270405, "dur": 36, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415270445, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415270448, "dur": 21988, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415292446, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415292452, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415292483, "dur": 5, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415292490, "dur": 1499, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415293999, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294005, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294051, "dur": 4, "ph": "X", "name": "ProcessMessages 2434", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294058, "dur": 34, "ph": "X", "name": "ReadAsync 2434", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294098, "dur": 3, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294104, "dur": 33, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294139, "dur": 2, "ph": "X", "name": "ProcessMessages 2257", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294143, "dur": 34, "ph": "X", "name": "ReadAsync 2257", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294184, "dur": 3, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294190, "dur": 30, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294222, "dur": 2, "ph": "X", "name": "ProcessMessages 1392", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294225, "dur": 25, "ph": "X", "name": "ReadAsync 1392", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294256, "dur": 3, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294262, "dur": 25, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294289, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294292, "dur": 23, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294318, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294320, "dur": 30, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294353, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294355, "dur": 30, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294388, "dur": 1, "ph": "X", "name": "ProcessMessages 1134", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294390, "dur": 17, "ph": "X", "name": "ReadAsync 1134", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294410, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294412, "dur": 19, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294433, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294435, "dur": 26, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294464, "dur": 1, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294466, "dur": 23, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294493, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294495, "dur": 21, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294518, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294521, "dur": 23, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294546, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294548, "dur": 21, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294571, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294573, "dur": 23, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294598, "dur": 1, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294601, "dur": 19, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294622, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294624, "dur": 22, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294648, "dur": 1, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294650, "dur": 20, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294672, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294675, "dur": 24, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294701, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294703, "dur": 19, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294725, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294727, "dur": 22, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294751, "dur": 1, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294754, "dur": 23, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294779, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294781, "dur": 19, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294803, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294805, "dur": 51, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294858, "dur": 1, "ph": "X", "name": "ProcessMessages 1639", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294861, "dur": 21, "ph": "X", "name": "ReadAsync 1639", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294885, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294887, "dur": 20, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294910, "dur": 1, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294912, "dur": 23, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294937, "dur": 1, "ph": "X", "name": "ProcessMessages 1013", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294939, "dur": 23, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294964, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294967, "dur": 22, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294991, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415294993, "dur": 19, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295014, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295017, "dur": 28, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295047, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295050, "dur": 36, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295089, "dur": 1, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295091, "dur": 25, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295119, "dur": 2, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295123, "dur": 30, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295155, "dur": 2, "ph": "X", "name": "ProcessMessages 1092", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295158, "dur": 316, "ph": "X", "name": "ReadAsync 1092", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295477, "dur": 7, "ph": "X", "name": "ProcessMessages 8542", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295486, "dur": 135, "ph": "X", "name": "ReadAsync 8542", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295624, "dur": 2, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295627, "dur": 38, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295667, "dur": 3, "ph": "X", "name": "ProcessMessages 2975", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295672, "dur": 25, "ph": "X", "name": "ReadAsync 2975", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295700, "dur": 1, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295702, "dur": 17, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295722, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295724, "dur": 22, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295748, "dur": 1, "ph": "X", "name": "ProcessMessages 967", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295751, "dur": 19, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295771, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295774, "dur": 21, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295797, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295800, "dur": 22, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295824, "dur": 1, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295826, "dur": 20, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295848, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295850, "dur": 25, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295878, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295880, "dur": 21, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295903, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295905, "dur": 22, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295929, "dur": 1, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295931, "dur": 20, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295953, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295956, "dur": 20, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295978, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415295980, "dur": 26, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296008, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296011, "dur": 18, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296030, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296033, "dur": 22, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296057, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296059, "dur": 22, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296084, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296086, "dur": 21, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296109, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296112, "dur": 19, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296133, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296135, "dur": 30, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296167, "dur": 1, "ph": "X", "name": "ProcessMessages 1005", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296169, "dur": 22, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296193, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296196, "dur": 19, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296217, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296219, "dur": 22, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296243, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296245, "dur": 21, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296269, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296271, "dur": 22, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296295, "dur": 1, "ph": "X", "name": "ProcessMessages 943", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296297, "dur": 21, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296320, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296323, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296346, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296349, "dur": 22, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296372, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296375, "dur": 18, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296395, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296397, "dur": 129, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296533, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296537, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296565, "dur": 2, "ph": "X", "name": "ProcessMessages 1378", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296569, "dur": 24, "ph": "X", "name": "ReadAsync 1378", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296596, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296598, "dur": 20, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296620, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296622, "dur": 55, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296679, "dur": 1, "ph": "X", "name": "ProcessMessages 1035", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296682, "dur": 22, "ph": "X", "name": "ReadAsync 1035", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296706, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296708, "dur": 18, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296729, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296731, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296755, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296757, "dur": 23, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296782, "dur": 1, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296785, "dur": 22, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296809, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296811, "dur": 20, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296833, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296836, "dur": 23, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296861, "dur": 1, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296863, "dur": 20, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296885, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296887, "dur": 24, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296913, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296916, "dur": 28, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296946, "dur": 1, "ph": "X", "name": "ProcessMessages 1158", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296949, "dur": 22, "ph": "X", "name": "ReadAsync 1158", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296973, "dur": 1, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296975, "dur": 21, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415296998, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297001, "dur": 29, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297033, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297036, "dur": 18, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297057, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297080, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297082, "dur": 22, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297106, "dur": 1, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297109, "dur": 22, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297132, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297135, "dur": 21, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297158, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297160, "dur": 23, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297185, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297187, "dur": 19, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297209, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297211, "dur": 40, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297253, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297255, "dur": 19, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297276, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297278, "dur": 20, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297301, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297303, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297329, "dur": 1, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297331, "dur": 19, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297353, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297355, "dur": 22, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297379, "dur": 1, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297382, "dur": 18, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297402, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297404, "dur": 24, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297430, "dur": 1, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297432, "dur": 23, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297458, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297460, "dur": 22, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297484, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297486, "dur": 22, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297510, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297512, "dur": 19, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297534, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297536, "dur": 321, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297863, "dur": 10, "ph": "X", "name": "ProcessMessages 6040", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297876, "dur": 45, "ph": "X", "name": "ReadAsync 6040", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297926, "dur": 4, "ph": "X", "name": "ProcessMessages 1308", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297932, "dur": 28, "ph": "X", "name": "ReadAsync 1308", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297962, "dur": 2, "ph": "X", "name": "ProcessMessages 1011", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297965, "dur": 31, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415297998, "dur": 1, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298001, "dur": 18, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298022, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298024, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298049, "dur": 1, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298052, "dur": 21, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298075, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298077, "dur": 20, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298100, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298102, "dur": 24, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298128, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298131, "dur": 23, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298156, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298158, "dur": 21, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298181, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298183, "dur": 19, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298204, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298206, "dur": 21, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298230, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298232, "dur": 23, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298257, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298259, "dur": 23, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298284, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298287, "dur": 22, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298311, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298313, "dur": 21, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298337, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298339, "dur": 21, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298362, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298364, "dur": 18, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298384, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298386, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298407, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298409, "dur": 26, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298438, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298441, "dur": 22, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298465, "dur": 2, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298468, "dur": 22, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298492, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298494, "dur": 24, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298520, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298523, "dur": 29, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298554, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298557, "dur": 25, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298586, "dur": 2, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298590, "dur": 38, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298631, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298634, "dur": 227, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298867, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298871, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298920, "dur": 7, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298929, "dur": 56, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298991, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415298997, "dur": 94, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299093, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299096, "dur": 86, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299184, "dur": 4, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299190, "dur": 28, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299224, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299228, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299265, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299269, "dur": 68, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299341, "dur": 3, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299346, "dur": 98, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299460, "dur": 21, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299487, "dur": 204, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299694, "dur": 4, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299700, "dur": 179, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299894, "dur": 4, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415299901, "dur": 176, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415300092, "dur": 1366, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415301466, "dur": 48, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415301522, "dur": 2134, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415303664, "dur": 46, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415303714, "dur": 11, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415303726, "dur": 33, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415303762, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415303764, "dur": 262, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304033, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304037, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304135, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304138, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304165, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304167, "dur": 102, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304273, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304379, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304382, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304412, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304415, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304441, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304444, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304467, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304469, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304497, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304499, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304522, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304524, "dur": 221, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304751, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304755, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304807, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304812, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304871, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304876, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304906, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415304908, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305045, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305048, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305105, "dur": 4, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305112, "dur": 40, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305155, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305158, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305211, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305215, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305305, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305310, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305355, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305359, "dur": 41, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305403, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305406, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305450, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305453, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305474, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305477, "dur": 100, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305582, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305585, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305624, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305627, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305661, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305663, "dur": 54, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305723, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305727, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305773, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305778, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305801, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415305996, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306000, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306068, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306071, "dur": 43, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306118, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306157, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306162, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306207, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306211, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306237, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306239, "dur": 204, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306450, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306455, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306485, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306489, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306512, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306515, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306543, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306546, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306589, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306594, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306616, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306619, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306682, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306702, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306704, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306859, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306933, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306938, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306976, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415306980, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415307021, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415307025, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415307073, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415307077, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415307105, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415307108, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415307155, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415307159, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415307188, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415307191, "dur": 14863, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322068, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322074, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322107, "dur": 4, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322112, "dur": 411, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322532, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322538, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322650, "dur": 4, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322657, "dur": 128, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322794, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322798, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322836, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415322840, "dur": 298, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415323145, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415323149, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415323253, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415323260, "dur": 660, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415323926, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415323930, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415323953, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415323957, "dur": 16469, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415340441, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415340449, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415340527, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415340537, "dur": 66160, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415406716, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415406724, "dur": 201, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415406936, "dur": 41, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415406981, "dur": 198, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415407188, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415407193, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415407220, "dur": 28, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415407251, "dur": 850, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415408108, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415408112, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415408144, "dur": 17, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415408164, "dur": 103, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415408271, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415408274, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415408307, "dur": 21, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415408331, "dur": 1558, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415409900, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415409906, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415409944, "dur": 24, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415409971, "dur": 4264, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415414248, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415414254, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415414282, "dur": 26, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415414310, "dur": 689, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415415007, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415415012, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415415040, "dur": 23, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415415065, "dur": 70, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415415142, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415415146, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415415169, "dur": 16, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415415187, "dur": 11590, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415426788, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415426793, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415426825, "dur": 27, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415426854, "dur": 15283, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415442147, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415442152, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415442184, "dur": 26, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415442211, "dur": 93, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415442311, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415442314, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415442336, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415442340, "dur": 49149, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415491501, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415491507, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415491611, "dur": 28, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415491642, "dur": 153, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415491799, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415491831, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707415491837, "dur": 1735991, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707417227839, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707417227845, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707417227892, "dur": 3431, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707417231331, "dur": 51, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707417231389, "dur": 35, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 3352, "tid": 21474836480, "ts": 1756707417231428, "dur": 2873, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 3352, "tid": 274, "ts": 1756707417240192, "dur": 1127, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3352, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3352, "tid": 17179869184, "ts": 1756707415261507, "dur": 5, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3352, "tid": 17179869184, "ts": 1756707415261512, "dur": 8859, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3352, "tid": 17179869184, "ts": 1756707415270373, "dur": 29, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3352, "tid": 274, "ts": 1756707417241323, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3352, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3352, "tid": 1, "ts": 1756707415034834, "dur": 3149, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3352, "tid": 1, "ts": 1756707415037987, "dur": 21078, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3352, "tid": 1, "ts": 1756707415059076, "dur": 25097, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3352, "tid": 274, "ts": 1756707417241335, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 3352, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415033675, "dur": 4541, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415038218, "dur": 51330, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415038803, "dur": 2105, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415040916, "dur": 1021, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415041944, "dur": 210, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042159, "dur": 23, "ph": "X", "name": "ProcessMessages 20482", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042185, "dur": 42, "ph": "X", "name": "ReadAsync 20482", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042229, "dur": 2, "ph": "X", "name": "ProcessMessages 1434", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042232, "dur": 20, "ph": "X", "name": "ReadAsync 1434", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042255, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042257, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042280, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042282, "dur": 19, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042305, "dur": 22, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042328, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042331, "dur": 18, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042351, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042353, "dur": 18, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042373, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042375, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042400, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042402, "dur": 23, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042427, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042430, "dur": 17, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042450, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042452, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042472, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042474, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042495, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042498, "dur": 19, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042519, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042521, "dur": 20, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042543, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042545, "dur": 19, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042567, "dur": 1, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042569, "dur": 24, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042595, "dur": 1, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042597, "dur": 21, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042620, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042622, "dur": 16, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042642, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042668, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042670, "dur": 20, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042692, "dur": 1, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042694, "dur": 25, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042721, "dur": 1, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042723, "dur": 18, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042744, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042746, "dur": 20, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042768, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042770, "dur": 22, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042794, "dur": 1, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042797, "dur": 21, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042820, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042822, "dur": 17, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042842, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415042844, "dur": 235, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043081, "dur": 5, "ph": "X", "name": "ProcessMessages 6165", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043088, "dur": 17, "ph": "X", "name": "ReadAsync 6165", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043108, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043110, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043133, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043135, "dur": 19, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043157, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043159, "dur": 38, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043199, "dur": 1, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043201, "dur": 128, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043335, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043339, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043370, "dur": 3, "ph": "X", "name": "ProcessMessages 1918", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043375, "dur": 29, "ph": "X", "name": "ReadAsync 1918", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043406, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043408, "dur": 24, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043439, "dur": 3, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043445, "dur": 27, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043475, "dur": 2, "ph": "X", "name": "ProcessMessages 1561", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043478, "dur": 20, "ph": "X", "name": "ReadAsync 1561", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043500, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043504, "dur": 19, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043526, "dur": 28, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043557, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043559, "dur": 40, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043601, "dur": 2, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043604, "dur": 21, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043627, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043630, "dur": 33, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043664, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043667, "dur": 22, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043691, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043693, "dur": 18, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043713, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043716, "dur": 19, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043737, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043739, "dur": 19, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043760, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043762, "dur": 21, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043785, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043787, "dur": 21, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043812, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043814, "dur": 21, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043837, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043839, "dur": 17, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043861, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043884, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043887, "dur": 18, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043907, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043909, "dur": 17, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043930, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043955, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043957, "dur": 17, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043976, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415043978, "dur": 25, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044005, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044007, "dur": 22, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044031, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044033, "dur": 20, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044055, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044057, "dur": 22, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044082, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044084, "dur": 19, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044105, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044107, "dur": 17, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044127, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044128, "dur": 24, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044155, "dur": 1, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044157, "dur": 23, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044183, "dur": 1, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044186, "dur": 47, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044235, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044237, "dur": 26, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044265, "dur": 2, "ph": "X", "name": "ProcessMessages 1367", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044268, "dur": 20, "ph": "X", "name": "ReadAsync 1367", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044291, "dur": 19, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044313, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044315, "dur": 22, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044339, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044342, "dur": 21, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044365, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044367, "dur": 23, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044392, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044395, "dur": 21, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044418, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044420, "dur": 21, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044443, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044445, "dur": 19, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044466, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044469, "dur": 18, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044492, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044494, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044517, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044519, "dur": 21, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044542, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044545, "dur": 19, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044567, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044569, "dur": 29, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044600, "dur": 1, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044602, "dur": 19, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044624, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044626, "dur": 20, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044648, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044650, "dur": 19, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044671, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044673, "dur": 21, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044697, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044699, "dur": 25, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044726, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044729, "dur": 21, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044752, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044754, "dur": 18, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044774, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044776, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044799, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044801, "dur": 20, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044823, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044826, "dur": 19, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044847, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044849, "dur": 29, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044880, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044882, "dur": 21, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044905, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044907, "dur": 21, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044930, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044934, "dur": 34, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044970, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044972, "dur": 21, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044996, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415044998, "dur": 18, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045019, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045021, "dur": 19, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045042, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045044, "dur": 18, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045064, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045066, "dur": 21, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045089, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045091, "dur": 21, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045115, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045117, "dur": 64, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045194, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045197, "dur": 24, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045223, "dur": 1, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045226, "dur": 194, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045437, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045449, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415045579, "dur": 556, "ph": "X", "name": "ProcessMessages 980", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415046140, "dur": 154, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415046300, "dur": 21, "ph": "X", "name": "ProcessMessages 2252", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415046324, "dur": 34, "ph": "X", "name": "ReadAsync 2252", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415046361, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415046364, "dur": 26, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415046393, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415046395, "dur": 136, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415046547, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415046560, "dur": 522, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415047089, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415047094, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415047150, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415047154, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415047189, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415047193, "dur": 2798, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415049998, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050002, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050052, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050056, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050092, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050095, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050120, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050122, "dur": 247, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050372, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050375, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050424, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050430, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050462, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050465, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050488, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050491, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050511, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050514, "dur": 162, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050680, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050713, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050716, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050748, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050751, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050895, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050897, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050930, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050932, "dur": 28, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050965, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050996, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415050998, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051027, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051029, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051062, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051067, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051100, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051102, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051130, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051260, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051286, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051336, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051360, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051362, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051386, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051388, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051433, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051438, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051479, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051484, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051525, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051527, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051555, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051558, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051583, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051616, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051619, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051646, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051648, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051676, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051704, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051706, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051744, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051748, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051775, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051779, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051815, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051820, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051844, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051847, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051931, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051956, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051959, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415051987, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052012, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052015, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052039, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052041, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052080, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052100, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052102, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052129, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052131, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052190, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052223, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052229, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052265, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052267, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052297, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052301, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052335, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052339, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052362, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052364, "dur": 101, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052470, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052490, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052492, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052514, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052517, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052608, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052639, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052673, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052711, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052713, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052739, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052742, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052763, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052918, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052950, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052954, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052980, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415052983, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053016, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053020, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053044, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053046, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053094, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053097, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053124, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053127, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053160, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053181, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053183, "dur": 170, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053358, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053381, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053384, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053422, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053443, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053446, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053470, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053492, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053620, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053640, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415053642, "dur": 32325, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415085976, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415085981, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415086005, "dur": 256, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3352, "tid": 12884901888, "ts": 1756707415086266, "dur": 3253, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3352, "tid": 274, "ts": 1756707417241346, "dur": 820, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3352, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3352, "tid": 8589934592, "ts": 1756707415032010, "dur": 52216, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3352, "tid": 8589934592, "ts": 1756707415084230, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3352, "tid": 8589934592, "ts": 1756707415084236, "dur": 757, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3352, "tid": 274, "ts": 1756707417242169, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3352, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3352, "tid": 4294967296, "ts": 1756707415019756, "dur": 70741, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3352, "tid": 4294967296, "ts": 1756707415023325, "dur": 4777, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3352, "tid": 4294967296, "ts": 1756707415090626, "dur": 169313, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 3352, "tid": 4294967296, "ts": 1756707415260061, "dur": 1974272, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3352, "tid": 4294967296, "ts": 1756707415260181, "dur": 1295, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3352, "tid": 4294967296, "ts": 1756707417234341, "dur": 2485, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3352, "tid": 4294967296, "ts": 1756707417235766, "dur": 37, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3352, "tid": 4294967296, "ts": 1756707417236831, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3352, "tid": 274, "ts": 1756707417242180, "dur": 23, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756707415270725, "dur": 22806, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415293536, "dur": 79, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415293663, "dur": 642, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415295763, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756707415297900, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756707415298115, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1756707415294320, "dur": 4621, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415298946, "dur": 1931406, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707417230384, "dur": 459, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707417231122, "dur": 416, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756707415294032, "dur": 4920, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415298958, "dur": 541, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415299508, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756707415299766, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756707415300090, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415300541, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415301152, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415301433, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415302135, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415302663, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415302973, "dur": 702, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Configuration\\AutoConfig.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756707415302824, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415303747, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415304049, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415304453, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415304670, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415304721, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415305178, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415305435, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415305747, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415306090, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415306538, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415306815, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415306911, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415307245, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415307425, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415307504, "dur": 1921437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707417228943, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1756707417228942, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1756707417229050, "dur": 1080, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1756707417230132, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415294059, "dur": 4904, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415298968, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_37D7122E47FD8E62.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756707415299215, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756707415299214, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_BE3A213ECBB164CF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756707415299355, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415299524, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756707415299776, "dur": 396, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756707415300172, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415300885, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415301517, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415302285, "dur": 616, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UVCSPluginIsEnabledPreference.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756707415302902, "dur": 692, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UVCSPlugin.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756707415302152, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415303626, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415304008, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415304424, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415304674, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415304729, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415305194, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415305394, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415305444, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415305713, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415306045, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415306099, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415306533, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415306821, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415306925, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415307239, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415307440, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415307517, "dur": 1922796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756707415294090, "dur": 4891, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756707415298983, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415298982, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_5A6D1923F79DD58D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415299273, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415299272, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_37FAA1C8C01FEF9A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415299517, "dur": 675, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_37FAA1C8C01FEF9A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415300221, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415301227, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415301714, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415302172, "dur": 493, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415302781, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415303405, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415303494, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\MetroSupport\\UnityEditor.UWP.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415301104, "dur": 2789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415304079, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415304459, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415304688, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415304760, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415305721, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415305825, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415306581, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415306957, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415307305, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415307501, "dur": 15365, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.VisualScripting.Shared.Editor.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415307501, "dur": 15368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_634EB3DA1CD7E5BE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415322889, "dur": 92450, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_634EB3DA1CD7E5BE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415415347, "dur": 1814986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415294049, "dur": 4908, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415298964, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_67CF7B0CA8ECC2F8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756707415299112, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756707415299111, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_71227717A57F940B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756707415299277, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756707415299276, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_D146C0A9813977F6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756707415299504, "dur": 554, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_D146C0A9813977F6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756707415300060, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415301022, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415301332, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415302283, "dur": 673, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_ListPool.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756707415301983, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415303121, "dur": 120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415303241, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415303404, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415303761, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415304059, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415304452, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415304734, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415305238, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415305437, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415305671, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415305746, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415306086, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415306549, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415306822, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415306926, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415307252, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415307433, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415307515, "dur": 1922791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415294085, "dur": 4891, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415299307, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415299442, "dur": 512, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756707415299957, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415302231, "dur": 626, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Exceptions\\InvalidImplementationException.cs"}}, {"pid": 12345, "tid": 5, "ts": 1756707415301691, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415302981, "dur": 797, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756707415302979, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415303865, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415303975, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415304040, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415304421, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415304691, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756707415304900, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415305186, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756707415305530, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415305645, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415305726, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756707415305789, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756707415306556, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415306945, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415307243, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415307425, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415307506, "dur": 1922792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415294118, "dur": 5014, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415299342, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415299517, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756707415299873, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415300037, "dur": 619, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\inspectors\\ClipInspector\\ClipInspector.cs"}}, {"pid": 12345, "tid": 6, "ts": 1756707415299997, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415301026, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415302194, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_TextParsingUtilities.cs"}}, {"pid": 12345, "tid": 6, "ts": 1756707415301962, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415303153, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415303347, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415303801, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415304023, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415304441, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415304748, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415305197, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415305433, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415305644, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415305760, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415306025, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415306077, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415306411, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415306534, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415306819, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415306924, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756707415307033, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756707415307443, "dur": 15682, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.VisualScripting.SettingsProvider.Editor.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1756707415307441, "dur": 15686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_DB47A982A779F40F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756707415323134, "dur": 91430, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_DB47A982A779F40F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756707415414574, "dur": 1815761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415294146, "dur": 5006, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415299322, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415299535, "dur": 320, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756707415299856, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415299978, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415300861, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415302061, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415302957, "dur": 725, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\RectMask2DEditor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1756707415302889, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415304010, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415304422, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415304695, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756707415304850, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415305141, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756707415305627, "dur": 16716, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.Rider.Editor.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756707415305626, "dur": 16720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_07D7D7B7487B1198.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756707415322385, "dur": 86225, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_07D7D7B7487B1198.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756707415408617, "dur": 1821663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415294169, "dur": 5078, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415299252, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1756707415299249, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F6E4D8F8E0EEA5EB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756707415299474, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415300747, "dur": 1149, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756707415301897, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415302273, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\Progress\\DrawProgressForOperations.cs"}}, {"pid": 12345, "tid": 8, "ts": 1756707415302273, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415303675, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415304019, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415304439, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415304718, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415305179, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415305342, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415305508, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415305728, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415305985, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415306084, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415306543, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415306818, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415306909, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415307239, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415307441, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415307515, "dur": 1922815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415294190, "dur": 4933, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415299250, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415299535, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_31698F8FE11825E4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756707415299854, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415300673, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415302255, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\_Deprecated\\WebApi\\OrganizationCredentials.cs"}}, {"pid": 12345, "tid": 9, "ts": 1756707415301998, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415302850, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415303228, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415303594, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415304153, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415304414, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415304691, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756707415304794, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756707415305175, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415305441, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756707415305537, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756707415306041, "dur": 34663, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.Timeline.Editor.ref.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756707415306040, "dur": 34668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0DA365A2C678CBF9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756707415340723, "dur": 101750, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0DA365A2C678CBF9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756707415442578, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756707415442484, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756707415442646, "dur": 49171, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756707415492043, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756707415491827, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756707415492139, "dur": 1735969, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756707417228938, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756707417228937, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756707417229019, "dur": 1131, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1756707415294213, "dur": 4895, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415299115, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1756707415299109, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_4B886728B1788745.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756707415299303, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415299477, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_6AB964CD495A3CF8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756707415299594, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1756707415300007, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415300748, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415301682, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415302946, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415303814, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415304042, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415304446, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415304778, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415305275, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415305453, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415305585, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415305753, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415306096, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415306544, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415306840, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415306920, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415307236, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415307505, "dur": 1922795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415294233, "dur": 4856, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415299097, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756707415299090, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_39ABCD1774A5D4DC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756707415299226, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415299307, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415299491, "dur": 281, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EFB246B7C973694.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756707415300525, "dur": 1003, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1756707415301991, "dur": 725, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\EqualityHandler.cs"}}, {"pid": 12345, "tid": 11, "ts": 1756707415301530, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415302894, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415303541, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415303603, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415304044, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415304449, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415304780, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415305188, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415305447, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415305750, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415306083, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415306533, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415306828, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415306935, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415307250, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415307427, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415307590, "dur": 1922692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415294253, "dur": 4886, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415299144, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1756707415299141, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_E309B8A6186F714E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756707415299285, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_E309B8A6186F714E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756707415299481, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1756707415299775, "dur": 651, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756707415300564, "dur": 569, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Events\\TriggerCustomEventDescriptor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1756707415300428, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415301563, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415302139, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415302978, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycastResult.cs"}}, {"pid": 12345, "tid": 12, "ts": 1756707415302978, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415304006, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415304411, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415304697, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756707415305004, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756707415305681, "dur": 17427, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.Performance.Profile-Analyzer.Editor.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1756707415305681, "dur": 17429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_7F7FA9B9E03669D2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756707415323119, "dur": 83884, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_7F7FA9B9E03669D2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756707415407020, "dur": 1823330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415294283, "dur": 4854, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415299144, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1756707415299138, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_A02194C7EADDF980.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756707415299307, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1756707415299306, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_8806FDA469071B5E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756707415299538, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415299593, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_8806FDA469071B5E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756707415299791, "dur": 322, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756707415300114, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415301210, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415302237, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Events\\MarkerTrack.cs"}}, {"pid": 12345, "tid": 13, "ts": 1756707415301947, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415302932, "dur": 752, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ToggleGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1756707415302906, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415303879, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415304051, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415304454, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415304742, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415305300, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415305432, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415305670, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415305741, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415306091, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415306529, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415306848, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415306908, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415307232, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415307428, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415307510, "dur": 1922835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415294304, "dur": 4822, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415299130, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1756707415299126, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_6315081EEBC439EC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756707415299272, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415299705, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756707415300044, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415300347, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415300820, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415301393, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415301958, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415302772, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415303473, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415303609, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415304097, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415304458, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415304699, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756707415304841, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756707415305987, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756707415306088, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756707415306366, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415306543, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415306825, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415306929, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415307240, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415307437, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415307513, "dur": 1922834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415294328, "dur": 4830, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415299191, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756707415299174, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_9E174EE4CC7849F7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756707415299286, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415299525, "dur": 410, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_9E174EE4CC7849F7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756707415299936, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415300080, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415300332, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415300559, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415301020, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415301681, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415302112, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415303407, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415303662, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415304024, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415304419, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415304743, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415305290, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415305433, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415305670, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415305752, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415306099, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415306537, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415306822, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415306927, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415307256, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415307428, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415307514, "dur": 1922826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415294345, "dur": 4859, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415299307, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756707415299306, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_996AF9CE86254B6E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756707415299501, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_996AF9CE86254B6E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756707415300486, "dur": 699, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1756707415301186, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415301661, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415302292, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\Tree\\CenteredContentPanel.cs"}}, {"pid": 12345, "tid": 16, "ts": 1756707415302232, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415303149, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415303315, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415303872, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415304003, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415304413, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415304688, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756707415304770, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756707415305359, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756707415305496, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415305642, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756707415306046, "dur": 17412, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.TextMeshPro.Editor.ref.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756707415306045, "dur": 17416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_37E92434B2D8A6CF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756707415323475, "dur": 84972, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_37E92434B2D8A6CF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756707415408454, "dur": 1821830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415294369, "dur": 4657, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415299110, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 17, "ts": 1756707415299110, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_6C99141102CE9A78.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756707415299224, "dur": 387, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1756707415299223, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_14E8EB4A80A51057.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756707415299643, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1756707415299937, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415300062, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415300612, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415301027, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415301323, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415302271, "dur": 570, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Collections\\FlexibleDictionary.cs"}}, {"pid": 12345, "tid": 17, "ts": 1756707415301894, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415303412, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415303603, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415304007, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415304425, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415304739, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415305208, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415305450, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415305745, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415306075, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415306344, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415306529, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415306813, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415306924, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415307231, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415307438, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415307507, "dur": 1922831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415294391, "dur": 4745, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415299249, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415299467, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1756707415299648, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1756707415299944, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415300745, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415301117, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415301503, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415302219, "dur": 524, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Views\\PendingChanges\\UnityPendingChangesTree.cs"}}, {"pid": 12345, "tid": 18, "ts": 1756707415302087, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415302957, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415303611, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415304012, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415304428, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415304692, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756707415304823, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756707415305236, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415305747, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756707415305807, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756707415306072, "dur": 18172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.TestTools.CodeCoverage.Editor.ref.dll"}}, {"pid": 12345, "tid": 18, "ts": 1756707415306072, "dur": 18174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_BD81B1708A4B02E0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756707415324256, "dur": 102853, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_BD81B1708A4B02E0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756707415427119, "dur": 1803120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415294410, "dur": 4738, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415299242, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1756707415299242, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7449DA1D28BC9A2E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756707415299470, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1756707415299707, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756707415299878, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415300156, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415300993, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415301471, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415302180, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_DefaultControls.cs"}}, {"pid": 12345, "tid": 19, "ts": 1756707415302860, "dur": 658, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMPro_ExtensionMethods.cs"}}, {"pid": 12345, "tid": 19, "ts": 1756707415301988, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415303646, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415304017, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415304426, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415304737, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415305447, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415305581, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415305737, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415306094, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415306578, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756707415306836, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415306912, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415307246, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415307431, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415307519, "dur": 1922772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415294426, "dur": 4696, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415299123, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_81F126235DCD4C90.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756707415299262, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_E5287B431E656A22.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756707415299499, "dur": 347, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756707415300952, "dur": 775, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756707415302773, "dur": 845, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorDelayedAttribute.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756707415301728, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415303618, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415304006, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415304468, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415304674, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415304777, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415305283, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415305403, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415305461, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415305734, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415306087, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415306544, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415306825, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415306905, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415307233, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415307421, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415307532, "dur": 1922811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415294447, "dur": 4581, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415299306, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\VisionOSPlayer\\UnityEditor.VisionOS.Extensions.dll"}}, {"pid": 12345, "tid": 21, "ts": 1756707415299305, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VisionOS.Extensions.dll_B8C006944E810474.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756707415299395, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756707415299788, "dur": 406, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1756707415300213, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415301015, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415301410, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415302285, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Views\\Labels\\LabelListViewItem.cs"}}, {"pid": 12345, "tid": 21, "ts": 1756707415302987, "dur": 708, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Views\\History\\HistoryListViewMenu.cs"}}, {"pid": 12345, "tid": 21, "ts": 1756707415302132, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415303816, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415304008, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415304435, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415304746, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415305295, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415305446, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415305738, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415306087, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415306545, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415306827, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415306931, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415307239, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415307442, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415307513, "dur": 1922813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415294466, "dur": 4547, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415299016, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1756707415299013, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_E57C9A315074D1E6.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756707415299223, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415299505, "dur": 541, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_E57C9A315074D1E6.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756707415300048, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415300464, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415301084, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415301457, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415302112, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415303252, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415303614, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415304027, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415304456, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415304744, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415305434, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415305674, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415305744, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415306089, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415306550, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415306932, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415307245, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415307436, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415307535, "dur": 1922752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415294485, "dur": 4524, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415299012, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415299010, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_CEAD06D2388CA1C9.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756707415299253, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415299500, "dur": 676, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756707415300176, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415300712, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415301352, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415302165, "dur": 762, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\CoverageFormats\\OpenCover\\Model\\Summary.cs"}}, {"pid": 12345, "tid": 23, "ts": 1756707415302971, "dur": 658, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.6\\Editor\\CoverageFormats\\OpenCover\\Model\\SequencePoint.cs"}}, {"pid": 12345, "tid": 23, "ts": 1756707415301997, "dur": 1677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415303674, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415304001, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415304412, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415304689, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756707415304817, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1756707415305439, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415305586, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415305708, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415306086, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415306548, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415306830, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415306927, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415307241, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415307434, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415307531, "dur": 1922844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415294503, "dur": 4625, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415299251, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415299565, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756707415299726, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756707415299860, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415299983, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415300137, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415300349, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415300922, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415301228, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415301696, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415302289, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Views\\Changesets\\ChangesetsTab.cs"}}, {"pid": 12345, "tid": 24, "ts": 1756707415302137, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415303156, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415303271, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415303429, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415303547, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415303709, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415304028, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415304444, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415304695, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756707415304850, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1756707415305352, "dur": 17795, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.VSCode.Editor.ref.dll"}}, {"pid": 12345, "tid": 24, "ts": 1756707415305351, "dur": 17798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VSCode.Editor.ref.dll_4366BA9A3B756B20.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756707415323156, "dur": 84373, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VSCode.Editor.ref.dll_4366BA9A3B756B20.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756707415407551, "dur": 1822723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415294529, "dur": 4480, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415299011, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1756707415299009, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_1346CDF0363449C4.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1756707415299315, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415299490, "dur": 683, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 25, "ts": 1756707415300174, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415300542, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415300774, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415301187, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Connections\\ControlConnection.cs"}}, {"pid": 12345, "tid": 25, "ts": 1756707415300956, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415301928, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415302660, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415302977, "dur": 669, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetsUtils\\ProjectPath.cs"}}, {"pid": 12345, "tid": 25, "ts": 1756707415302875, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415303774, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415304020, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415304442, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415304699, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1756707415304869, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1756707415305351, "dur": 16999, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.EditorCoroutines.Editor.ref.dll"}}, {"pid": 12345, "tid": 25, "ts": 1756707415305350, "dur": 17004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_A9E741B2F4D7FC42.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1756707415322397, "dur": 93094, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_A9E741B2F4D7FC42.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1756707415415498, "dur": 1814760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415294549, "dur": 4458, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415299009, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1756707415299008, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_2CAD531519D13EEF.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1756707415299223, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_F02AE6D8B1D25045.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1756707415299312, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415299397, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 26, "ts": 1756707415299706, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 26, "ts": 1756707415299979, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415300336, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415300606, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415300834, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415301520, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415302268, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsDirectConverter.cs"}}, {"pid": 12345, "tid": 26, "ts": 1756707415301790, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415302974, "dur": 658, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetOverlays\\AssetStatus.cs"}}, {"pid": 12345, "tid": 26, "ts": 1756707415302882, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415303830, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415304002, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415304431, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415304725, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415305248, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415305397, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415305456, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415305729, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415306023, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415306078, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415306539, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415306820, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415306921, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415307245, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415307447, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415307509, "dur": 1922725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415294570, "dur": 4533, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415299210, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415299299, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.dll"}}, {"pid": 12345, "tid": 27, "ts": 1756707415299298, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_8D5493305280620E.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1756707415299495, "dur": 744, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1756707415300241, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415301258, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415301780, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415302970, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\MetaPath.cs"}}, {"pid": 12345, "tid": 27, "ts": 1756707415302701, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415303656, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415303978, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415304098, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415304419, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415304740, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415305209, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415305448, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415305581, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415305710, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415306088, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415306547, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415306814, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415306918, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415307247, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415307430, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415307535, "dur": 1922750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415294590, "dur": 4553, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415299259, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_0A2F7108C7922039.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1756707415299483, "dur": 684, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1756707415300167, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415301102, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415301590, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415302164, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415302705, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415302986, "dur": 788, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\IUtpLogger.cs"}}, {"pid": 12345, "tid": 28, "ts": 1756707415302986, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415303890, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415304022, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415304440, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415304727, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415305174, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415305441, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415305583, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415305717, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415305978, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415306100, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415306524, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415306813, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415306904, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415307233, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415307429, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415307530, "dur": 1922769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415294616, "dur": 4403, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415299050, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1756707415299032, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_504CC3D318BDE927.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1756707415299228, "dur": 280, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 29, "ts": 1756707415299212, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_B480793001CC804F.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1756707415299517, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_B480793001CC804F.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1756707415299716, "dur": 481, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1756707415300203, "dur": 1084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1756707415301349, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1756707415301463, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1756707415301715, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1756707415301788, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1756707415301859, "dur": 327, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1756707415302271, "dur": 734, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 29, "ts": 1756707415303109, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 29, "ts": 1756707415303391, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 29, "ts": 1756707415301307, "dur": 2589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 29, "ts": 1756707415304006, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415304420, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415304744, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415305307, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415305390, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415305469, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415305585, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415305750, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415306008, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1756707415306131, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 29, "ts": 1756707415306349, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415306534, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415306819, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415306948, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415307234, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415307434, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415307518, "dur": 1922790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415294639, "dur": 4522, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415299226, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 30, "ts": 1756707415299163, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_FDE04BF8CDD5BA61.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1756707415299486, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_FDE04BF8CDD5BA61.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1756707415299852, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415299964, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415300140, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415300615, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415301204, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415301435, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415302219, "dur": 609, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Connections\\ConnectionCollectionBase.cs"}}, {"pid": 12345, "tid": 30, "ts": 1756707415301848, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415303108, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415303223, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415303373, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415303766, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415304018, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415304435, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415304741, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415305201, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415305438, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415305718, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415306094, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415306539, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415306985, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415307238, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415307443, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415307508, "dur": 1922838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415294660, "dur": 4457, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415299174, "dur": 384, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1756707415299173, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_B9D17F4A7C59EB9F.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1756707415299719, "dur": 405, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1756707415300127, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415300501, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415300889, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415301400, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415302979, "dur": 713, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Views\\Labels\\LabelsTab_Operations.cs"}}, {"pid": 12345, "tid": 31, "ts": 1756707415302122, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415303692, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415304032, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415304447, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415304696, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1756707415304782, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 31, "ts": 1756707415305173, "dur": 17657, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.VisualStudio.Editor.ref.dll"}}, {"pid": 12345, "tid": 31, "ts": 1756707415305173, "dur": 17659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_0788C6E0BDFBBC54.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1756707415322839, "dur": 87397, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_0788C6E0BDFBBC54.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1756707415410244, "dur": 1820074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415294680, "dur": 4311, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415299091, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415299235, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 32, "ts": 1756707415299235, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_1EDABEBBAF3B5AF6.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1756707415299477, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1756707415299776, "dur": 418, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 32, "ts": 1756707415300222, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415300866, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415301087, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415301448, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415302219, "dur": 912, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsTypeConverter.cs"}}, {"pid": 12345, "tid": 32, "ts": 1756707415301823, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415303169, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415303386, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415303844, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415304043, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415304451, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415304735, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415305173, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415305408, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415305458, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415305672, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415305742, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415305981, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415306081, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415306541, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415306825, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415306937, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415307246, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415307424, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415307506, "dur": 1922743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707417232988, "dur": 1327, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1756707415128788, "dur": 118531, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756707415129468, "dur": 33335, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756707415230343, "dur": 3245, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756707415233589, "dur": 13725, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756707415234271, "dur": 11451, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756707415251443, "dur": 910, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756707415250990, "dur": 1513, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756707415036772, "dur": 1208, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415037988, "dur": 203, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415038251, "dur": 690, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415039502, "dur": 1077, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_9F15594838CD0D60.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756707415041145, "dur": 1214, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1756707415038956, "dur": 6466, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415045426, "dur": 40009, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415085436, "dur": 303, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415085835, "dur": 123, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415086184, "dur": 459, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756707415038549, "dur": 6882, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415045644, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756707415045643, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_24C6155DDFF204C8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756707415045920, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756707415046147, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756707415046384, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415047182, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415047767, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415048748, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\EditorDispatcher.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756707415048733, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415049630, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415049967, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415050281, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415050652, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415050979, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756707415051604, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756707415051191, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756707415051977, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756707415052052, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756707415052430, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415052890, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415052941, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415053205, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415053314, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756707415053722, "dur": 31768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415038614, "dur": 6855, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415045727, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_1346CDF0363449C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756707415045889, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415046060, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415046113, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756707415046301, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415047299, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415047728, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415048178, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415049304, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\Errors\\ErrorsPanel.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756707415048723, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415050032, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415050299, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415050665, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415051010, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756707415051268, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756707415051204, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756707415051829, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415051900, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415051956, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415052224, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415052519, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415052920, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415053194, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415053308, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756707415053648, "dur": 31769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756707415038577, "dur": 6864, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756707415045732, "dur": 843, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_54ABF8F57495F9AA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415046578, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415047401, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415047477, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415047663, "dur": 285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415047960, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415048455, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415048669, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415048759, "dur": 340, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415049100, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415049514, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415049629, "dur": 272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415049902, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415047401, "dur": 2841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415050330, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415050737, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415050979, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415051267, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756707415051716, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Internal\\fsCyclicReferenceManager.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756707415051106, "dur": 1029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415052244, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756707415052303, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415052956, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756707415053195, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756707415053306, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756707415053644, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756707415053863, "dur": 31578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415038603, "dur": 6843, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415045719, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_EBC612ACAC2A0AE0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756707415045791, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756707415045790, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_8D5493305280620E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756707415045887, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415046205, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415047253, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415048717, "dur": 775, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Views\\Changesets\\ChangesetsTab.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756707415048649, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415050058, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415050296, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415050661, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415051019, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756707415051268, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756707415051267, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756707415051908, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415051967, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415052239, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415052455, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415052923, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415053220, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415053334, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756707415053658, "dur": 31889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415038635, "dur": 6855, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415045650, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415045825, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\VisionOSPlayer\\UnityEditor.VisionOS.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756707415045824, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VisionOS.Extensions.dll_B8C006944E810474.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756707415046202, "dur": 129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415046331, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415046480, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415047430, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415047791, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415048687, "dur": 581, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\_Deprecated\\CollabMigration\\MigrateCollabProject.cs"}}, {"pid": 12345, "tid": 5, "ts": 1756707415048527, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415049498, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415049883, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415049968, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415050276, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415050715, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415051031, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415051545, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415051818, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415051967, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415052231, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415052474, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415052936, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415053215, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415053323, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756707415053685, "dur": 32112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415038662, "dur": 7086, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415045753, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 6, "ts": 1756707415045749, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_01E03F9D9B63094A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756707415045980, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756707415046124, "dur": 512, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756707415046639, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415047296, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415047758, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415048664, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\AssetUpgrade\\TimelineUpgrade.cs"}}, {"pid": 12345, "tid": 6, "ts": 1756707415048386, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415049346, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415050041, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415050292, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415050665, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415050986, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756707415051118, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415051221, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756707415051697, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756707415051970, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756707415052575, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415052885, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415052953, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415053202, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415053316, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756707415053726, "dur": 31755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415038681, "dur": 7061, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415045808, "dur": 251, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_37FAA1C8C01FEF9A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756707415046099, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415046186, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.Entities.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756707415046256, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415046345, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415047583, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415047909, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415048714, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415049744, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415050037, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415050288, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415050671, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415050988, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756707415051292, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756707415051826, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415051905, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415051963, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415052234, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415052448, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415052930, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415053201, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415053309, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756707415053740, "dur": 31745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415038708, "dur": 7033, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415045759, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_D146C0A9813977F6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756707415046118, "dur": 442, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756707415046561, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415047342, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415047911, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415048673, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415049576, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415049906, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415049985, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415050345, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415050693, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415051021, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415051553, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415051713, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415051801, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415051896, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415051966, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415052214, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415052435, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415052925, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415053191, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415053302, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756707415053649, "dur": 32268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415038730, "dur": 7075, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415045818, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\VisionOSPlayer\\UnityEditor.iOS.Extensions.Common.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756707415045807, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_B75C7A17C4BB0952.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756707415046034, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415046253, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415046429, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415047559, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415048662, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsConverterRegistrar.cs"}}, {"pid": 12345, "tid": 9, "ts": 1756707415048177, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415049562, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415050036, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415050282, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415050661, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415051054, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415051557, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415051811, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415051882, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415051940, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415052228, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415052478, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415052914, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415053192, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415053397, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756707415053594, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756707415053654, "dur": 31866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415038751, "dur": 7069, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415045957, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756707415046223, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415046369, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415046649, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415046910, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415047710, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415047999, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415048748, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Views\\CreateWorkspace\\PerformInitialCheckin.cs"}}, {"pid": 12345, "tid": 10, "ts": 1756707415048614, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415049524, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415050011, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415050289, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415050657, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415050962, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415051190, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415051534, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415051685, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415051802, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415052011, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415052225, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415052499, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415052919, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415053198, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415053313, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756707415053731, "dur": 31782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415039035, "dur": 6718, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415045755, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_930569E5D547690B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756707415045937, "dur": 461, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1756707415046400, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415047317, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415047987, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415048210, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415048740, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\VCSBuiltInPlugin.cs"}}, {"pid": 12345, "tid": 11, "ts": 1756707415048649, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415049762, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415050076, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415050287, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415050666, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415051026, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415051538, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415051682, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415051806, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415051890, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415051951, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415052226, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415052481, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415052924, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415053211, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415053324, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756707415053699, "dur": 31805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415039057, "dur": 6739, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415045894, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415046102, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1756707415046345, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415047057, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415047682, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415048036, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415049287, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415049998, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415050377, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415050688, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415051034, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415051611, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415051689, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415051793, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415051891, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415051962, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415052217, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415052409, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415052712, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415052952, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415053190, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415053303, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756707415053646, "dur": 31772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415038822, "dur": 6920, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415045763, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_6AB964CD495A3CF8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756707415046014, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756707415046080, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415046136, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415046305, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.Entities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1756707415046565, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415048364, "dur": 926, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_0_2.cs"}}, {"pid": 12345, "tid": 13, "ts": 1756707415047582, "dur": 1820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415049402, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415050087, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415050307, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415050686, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415051037, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415051546, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415051689, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415051884, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415051939, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756707415052247, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756707415052312, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756707415052972, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756707415053389, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756707415053699, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756707415053856, "dur": 31591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415038842, "dur": 7089, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415045937, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415046027, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415046085, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1756707415046343, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415046738, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415047222, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415047763, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415049157, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415049653, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415050182, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415050306, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415050675, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415051057, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415051548, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415051717, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415051970, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415052218, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415052543, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415052937, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415053221, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415053330, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756707415053663, "dur": 32152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415038861, "dur": 6924, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415045811, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Common.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756707415045787, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_07F65D445061F227.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756707415046011, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756707415046158, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756707415046238, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415046364, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415046716, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415047335, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415047904, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415048524, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415049575, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415049782, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415049980, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415050385, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415050656, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415051020, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415051563, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415051697, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415051812, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415051882, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415051957, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415052233, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415052466, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415052930, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415053213, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415053323, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756707415053677, "dur": 32128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415038882, "dur": 7073, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415045957, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756707415046174, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756707415046276, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415047032, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415048111, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415048718, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415049199, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415050077, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415050301, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415050674, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415051033, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415051552, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415051685, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415051828, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415051889, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415051951, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415052229, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415052487, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415052884, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415052955, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415053193, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415053328, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756707415053668, "dur": 31857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415038911, "dur": 6997, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415045982, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415046188, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756707415046305, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756707415046583, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415047910, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415048759, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415049514, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415050016, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415050300, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415050670, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415050995, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756707415051225, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756707415051813, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415051940, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415052215, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415052414, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415052741, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415052935, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415053208, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415053321, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756707415053704, "dur": 31778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415038932, "dur": 6806, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415045761, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F6E4D8F8E0EEA5EB.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756707415046099, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756707415046218, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415047123, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415047655, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415047904, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415048504, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415049487, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415049978, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415050339, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415050684, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415051030, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415051613, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415051723, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415051791, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415051945, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415052222, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415052512, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415052886, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415052949, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415053193, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415053326, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756707415053681, "dur": 31845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415038955, "dur": 6821, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415045929, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756707415046017, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756707415046082, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756707415046232, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415046433, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415047111, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415047671, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415048644, "dur": 604, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Views\\PendingChanges\\PendingChangesTreeHeaderState.cs"}}, {"pid": 12345, "tid": 19, "ts": 1756707415048552, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415049470, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415049903, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415049967, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415050346, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415050682, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415051026, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415051542, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415051696, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415051795, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415051899, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415051958, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415052238, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415052441, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415052883, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415052953, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415053195, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415053312, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756707415053652, "dur": 31763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415038978, "dur": 6875, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415045926, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756707415046016, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756707415046145, "dur": 334, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1756707415046480, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415046715, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415047067, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415047386, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415049127, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415049263, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415049936, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415050277, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415050653, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415050960, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415051056, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415051559, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415051723, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415051810, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415052019, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415052222, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415052527, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415052889, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415052941, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415053225, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415053305, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415053644, "dur": 31580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756707415085225, "dur": 142, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415038996, "dur": 6734, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415045748, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7449DA1D28BC9A2E.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756707415046150, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1756707415046276, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415046797, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415047473, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415048433, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415049678, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415050005, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415050284, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415050669, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415050989, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756707415051092, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415051149, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756707415051629, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415052001, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415052218, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415052413, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756707415052476, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756707415052707, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415052920, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415053190, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415053302, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756707415053648, "dur": 31768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415039015, "dur": 6710, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415045741, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_E3940F2DAD489285.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756707415046059, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415046111, "dur": 433, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1756707415046545, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415047005, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415047751, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415048135, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415048376, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415049381, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415049797, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415049866, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415049932, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415050289, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415050664, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415050982, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756707415051604, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 22, "ts": 1756707415051112, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756707415051959, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756707415052226, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415052491, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415052939, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415053200, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415053319, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756707415053713, "dur": 31888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415038775, "dur": 6928, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415045740, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_31698F8FE11825E4.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756707415046003, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415046102, "dur": 479, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1756707415046582, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756707415047272, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415047472, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415047713, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415047947, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415048050, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415048216, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415048355, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415048683, "dur": 522, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415049217, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415049324, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415049468, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415049774, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756707415047233, "dur": 2963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1756707415050316, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415050695, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415051035, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415051553, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415051722, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415051797, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415051888, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415051949, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415052240, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415052434, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415052888, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415052947, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415053199, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415053311, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756707415053647, "dur": 31787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415038798, "dur": 6937, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415045757, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_5EB551823737F081.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756707415045971, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415046092, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1756707415046197, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756707415046252, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415046345, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415046858, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415048051, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415049301, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415049661, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415050090, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415050306, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415050678, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415051041, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415051539, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415051693, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415051887, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415051945, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415052220, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415052538, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415052935, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415053204, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415053308, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756707415053665, "dur": 31814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415039079, "dur": 6613, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415045735, "dur": 354, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_B480793001CC804F.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1756707415046112, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415046163, "dur": 405, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1756707415046568, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415047305, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415047664, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415048715, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Internal\\fsCyclicReferenceManager.cs"}}, {"pid": 12345, "tid": 25, "ts": 1756707415048152, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415049283, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415049955, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415050302, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415050678, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415051005, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1756707415051233, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415051301, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1756707415051602, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415051792, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415051885, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415051938, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415052008, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415052231, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415052471, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415052936, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415053217, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415053325, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756707415053690, "dur": 32029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415039098, "dur": 6558, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415045731, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E83F85F5B2DABDF5.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1756707415046018, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1756707415046104, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415046161, "dur": 422, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 26, "ts": 1756707415046584, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415047729, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415048561, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415049177, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415049371, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415049575, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415049974, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415050289, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415050660, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415050979, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1756707415051092, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1756707415051588, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415051701, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415051796, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415051880, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415051969, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415052234, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415052451, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415052884, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415052954, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415053208, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415053320, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756707415053695, "dur": 31789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415039130, "dur": 6705, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415045837, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1756707415045836, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_37D7122E47FD8E62.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1756707415046147, "dur": 358, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1756707415046506, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415047791, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415048297, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415048591, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415049213, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415049589, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415050062, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415050379, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415050692, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415051058, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415051562, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415051717, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415051815, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415051895, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415051954, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415052227, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415052485, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415052929, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415053219, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415053327, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756707415053672, "dur": 32138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415039152, "dur": 6608, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415045840, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415046128, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.Entities.rsp2"}}, {"pid": 12345, "tid": 28, "ts": 1756707415046263, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415047074, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415047951, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415049160, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415049613, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415050138, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415050312, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415050674, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415051039, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415051612, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415051728, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415051837, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415051907, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415051972, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415052221, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415052504, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415052887, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415052947, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415053203, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415053317, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756707415053708, "dur": 32003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415039171, "dur": 6528, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415045754, "dur": 314, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_14E8EB4A80A51057.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1756707415046112, "dur": 458, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 29, "ts": 1756707415046571, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415047256, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415047716, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415048402, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415048596, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415049394, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415049808, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415049869, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415049952, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415050280, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415050660, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415050977, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1756707415051716, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetsUtils\\SaveAssets.cs"}}, {"pid": 12345, "tid": 29, "ts": 1756707415051331, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 29, "ts": 1756707415052412, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 29, "ts": 1756707415052508, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 29, "ts": 1756707415052736, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415052932, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415053198, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415053310, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 29, "ts": 1756707415053744, "dur": 31849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415039191, "dur": 6332, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415045719, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_1EDABEBBAF3B5AF6.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1756707415045795, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Xcode.dll"}}, {"pid": 12345, "tid": 30, "ts": 1756707415045794, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10FC158E7947A415.mvfrm"}}, {"pid": 12345, "tid": 30, "ts": 1756707415045926, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 30, "ts": 1756707415045977, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415046217, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415046372, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415046507, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415047369, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415048312, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415048573, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415049396, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415049912, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415050374, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415050689, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415051042, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415051556, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415051706, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415051792, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415051883, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415051944, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415052213, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415052408, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415052578, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415052924, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415053205, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415053318, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 30, "ts": 1756707415053735, "dur": 31754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415039211, "dur": 6356, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415045568, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 31, "ts": 1756707415045568, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_5AF7E580F002F98D.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1756707415045730, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_0A2F7108C7922039.mvfrm"}}, {"pid": 12345, "tid": 31, "ts": 1756707415046039, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415046165, "dur": 378, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 31, "ts": 1756707415046543, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415046959, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415047585, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415047995, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415048480, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415049188, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415049988, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415050338, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415050680, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415051046, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415051567, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415051730, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415051814, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415051952, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415052232, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415052459, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415052934, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415053209, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415053321, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 31, "ts": 1756707415053718, "dur": 31788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415039228, "dur": 6271, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415045724, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_F02AE6D8B1D25045.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1756707415045836, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\soft\\Unity\\2022.3.56f1c1\\2022.3.56f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 32, "ts": 1756707415045833, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9EFB246B7C973694.mvfrm"}}, {"pid": 12345, "tid": 32, "ts": 1756707415045920, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415045998, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415046099, "dur": 470, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 32, "ts": 1756707415046571, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415047452, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415048217, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415048744, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\Avatar\\GetAvatar.cs"}}, {"pid": 12345, "tid": 32, "ts": 1756707415048744, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415049486, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415050050, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415050304, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415050677, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415051028, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415051550, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415051692, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415051812, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415051889, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415051945, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415052219, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415052532, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415052930, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415053196, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415053313, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 32, "ts": 1756707415053645, "dur": 31790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756707415088071, "dur": 1253, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3352, "tid": 274, "ts": 1756707417242560, "dur": 1070, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 3352, "tid": 274, "ts": 1756707417244893, "dur": 34, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 3352, "tid": 274, "ts": 1756707417245074, "dur": 19, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3352, "tid": 274, "ts": 1756707417243750, "dur": 1140, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 3352, "tid": 274, "ts": 1756707417244967, "dur": 106, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 3352, "tid": 274, "ts": 1756707417245110, "dur": 505, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3352, "tid": 274, "ts": 1756707417239035, "dur": 7117, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}